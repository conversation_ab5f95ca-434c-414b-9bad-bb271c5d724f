import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FlowExecution, RpaFlow, Customer } from '@rpa-project/shared'
import { executionApi, flowApi, customerApi } from '../services/api'

export function ExecutionList() {
  const [executions, setExecutions] = useState<FlowExecution[]>([])
  const [flows, setFlows] = useState<RpaFlow[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadData()

    // Auto-refresh every 5 seconds
    const interval = setInterval(loadData, 5000)
    return () => clearInterval(interval)
  }, [])

  const loadData = async () => {
    try {
      const [executionsResponse, flowsResponse, customersResponse] = await Promise.all([
        executionApi.getExecutions(),
        flowApi.getFlows(),
        customerApi.getCustomers()
      ])

      setExecutions(executionsResponse.data || [])
      setFlows(flowsResponse.data || [])
      setCustomers(customersResponse.data || [])
      setError(null)
    } catch (err) {
      setError('Misslyckades att ladda data')
      console.error('Error loading data:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadExecutions = async () => {
    try {
      const response = await executionApi.getExecutions()
      setExecutions(response.data || [])
      setError(null)
    } catch (err) {
      setError('Misslyckades att ladda körningar')
      console.error('Error loading executions:', err)
    }
  }

  const handleCancelExecution = async (id: string) => {
    if (!confirm('Är du säker på att du vill avbryta denna körning?')) {
      return
    }

    try {
      await executionApi.cancelExecution(id)
      await loadExecutions() // Refresh list
    } catch (err) {
      setError('Misslyckades att avbryta körning')
      console.error('Error canceling execution:', err)
    }
  }

  const getFlowName = (flowId: string): string => {
    const flow = flows.find(f => f.id === flowId)
    return flow?.name || `Okänt skript (${flowId.slice(0, 8)}...)`
  }

  const getCustomerInfo = (customerId: string): { customerNumber: string; customerName: string } => {
    const customer = customers.find(c => c.id === customerId)
    return {
      customerNumber: customer?.customerNumber || 'Okänt',
      customerName: customer?.name || 'Okänd kund'
    }
  }

  const formatDuration = (start: Date, end?: Date) => {
    const startTime = new Date(start).getTime()
    const endTime = end ? new Date(end).getTime() : Date.now()
    const duration = endTime - startTime

    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${Math.round(duration / 1000)}s`
    return `${Math.round(duration / 60000)}m`
  }

  const getStatusButtonClass = (status: string) => {
    const baseClass = "status-button"

    switch (status) {
      case 'completed':
        return `${baseClass} status-completed`
      case 'running':
        return `${baseClass} status-running`
      case 'pending':
        return `${baseClass} status-scheduled`
      case 'failed':
        return `${baseClass} status-failed`
      case 'cancelled':
        return `${baseClass} status-failed`
      default:
        return `${baseClass} status-scheduled`
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Slutförd'
      case 'running':
        return 'Körs'
      case 'pending':
        return 'Väntar'
      case 'failed':
        return 'Misslyckad'
      case 'cancelled':
        return 'Avbruten'
      default:
        return status
    }
  }

  if (loading && executions.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar körningar...</div>
      </div>
    )
  }

  const getExecutionStats = () => {
    const total = executions.length
    const completed = executions.filter(e => e.status === 'completed').length
    const running = executions.filter(e => e.status === 'running').length
    const failed = executions.filter(e => e.status === 'failed').length
    const pending = executions.filter(e => e.status === 'pending').length

    return { total, completed, running, failed, pending }
  }

  const stats = getExecutionStats()

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Loggar</p>
          <p className="dashboard-subtitle">
            Övervaka dina automatiseringskörningar och loggar.
          </p>
        </div>
        <button
          onClick={loadExecutions}
          className="action-button secondary"
          disabled={loading}
        >
          <span>{loading ? 'Uppdaterar...' : 'Uppdatera'}</span>
        </button>
      </div>

      {/* Stats Cards */}
      {executions.length > 0 && (
        <div className="stats-grid">
          <div className="stat-card">
            <p className="stat-label">Totalt antal körningar</p>
            <p className="stat-value">{stats.total}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Slutförda</p>
            <p className="stat-value">{stats.completed}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Körs nu</p>
            <p className="stat-value">{stats.running}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Misslyckade</p>
            <p className="stat-value">{stats.failed}</p>
            <p className="stat-change">+0%</p>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av körningar</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Executions List */}
      {executions.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🤖</div>
            <p className="empty-state-title">Inga körningar än</p>
            <p className="empty-state-subtitle">Kör ett flöde för att se resultat här</p>
            <Link to="/flows" className="action-button secondary centered">
              <span>Gå till flöde</span>
            </Link>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">Senaste körningar</h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Flödesnamn</th>
                    <th>Kundnummer</th>
                    <th>Kundnamn</th>
                    <th>Status</th>
                    <th>Startad</th>
                    <th>Varaktighet</th>
                    <th>Loggar</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {executions.map((execution) => (
                    <tr key={execution.id}>
                      <td>
                        <Link
                          to={`/executions/${execution.id}`}
                          className="flow-name-link"
                        >
                          {getFlowName(execution.flowId)}
                        </Link>
                      </td>
                      <td className="secondary-text">
                        {getCustomerInfo(execution.customerId).customerNumber}
                      </td>
                      <td className="secondary-text">
                        {getCustomerInfo(execution.customerId).customerName}
                      </td>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                          <button className={getStatusButtonClass(execution.status)}>
                            <span>{getStatusText(execution.status)}</span>
                          </button>
                          {(execution as any).retryInfo && (execution as any).retryInfo.isRetry && (
                            <span className="retry-badge" title={`Retry-försök: ${(execution as any).retryInfo.attempts}/${(execution as any).retryInfo.maxAttempts}`}>
                              🔄 {(execution as any).retryInfo.attempts}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="secondary-text">
                        {new Date(execution.startedAt).toLocaleString('sv-SE', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </td>
                      <td className="secondary-text">
                        {formatDuration(execution.startedAt, execution.completedAt)}
                      </td>
                      <td className="secondary-text">
                        {execution.logs.length}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <Link
                            to={`/executions/${execution.id}`}
                            className="action-button-small secondary"
                            title="Visa detaljer"
                          >
                            <span>Visa</span>
                          </Link>
                          {(execution.status === 'pending' || execution.status === 'running') && (
                            <button
                              onClick={() => handleCancelExecution(execution.id)}
                              className="action-button-small danger"
                              title="Avbryt körning"
                            >
                              <span>Avbryt</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
