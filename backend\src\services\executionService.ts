import {
  FlowExecution,
  ExecutionStatus,
  ExecutionLog,
  generateId
} from '@rpa-project/shared';
import { statements } from '../database/database';
import { QueueService } from '../queue/queueService';

// Temporary local definition until shared package is properly updated
interface RetryInfo {
  attempts: number;
  maxAttempts: number;
  isRetry: boolean;
}

interface ExecutionQuery {
  flowId?: string;
  status?: ExecutionStatus;
  limit?: number;
  offset?: number;
}

// SQLite-based storage
class ExecutionStorage {
  async save(execution: FlowExecution): Promise<FlowExecution> {
    try {
      const executionData = {
        id: execution.id,
        flow_id: execution.flowId,
        customer_id: execution.customerId,
        status: execution.status,
        started_at: execution.startedAt.toISOString(),
        results: JSON.stringify(execution.results || {})
      };

      statements.insertExecution.run(
        executionData.id,
        executionData.flow_id,
        executionData.customer_id,
        executionData.status,
        executionData.started_at,
        executionData.results
      );

      console.log(`💾 Saved execution: ${execution.id} for flow: ${execution.flowId}`);
      return execution;
    } catch (error) {
      console.error(`❌ Failed to save execution ${execution.id}:`, error);
      throw error;
    }
  }

  async findById(id: string): Promise<FlowExecution | null> {
    try {
      const row = statements.getExecutionById.get(id) as any;

      if (!row) {
        console.log(`🔍 Execution not found: ${id}`);
        return null;
      }

      // Get logs for this execution
      const logRows = statements.getExecutionLogs.all(id) as any[];
      const logs: ExecutionLog[] = logRows.map(logRow => ({
        timestamp: new Date(logRow.timestamp),
        level: logRow.level as 'info' | 'warn' | 'error' | 'debug',
        message: logRow.message,
        stepId: logRow.step_id || undefined,
        data: logRow.data ? JSON.parse(logRow.data) : undefined
      }));

      const execution: FlowExecution = {
        id: row.id,
        flowId: row.flow_id,
        customerId: row.customer_id,
        status: row.status as ExecutionStatus,
        startedAt: new Date(row.started_at),
        completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
        results: JSON.parse(row.results || '{}'),
        error: row.error || undefined,
        logs
      };

      console.log(`✅ Found execution: ${id} - status: ${execution.status}`);
      return execution;
    } catch (error) {
      console.error(`❌ Failed to find execution ${id}:`, error);
      throw error;
    }
  }

  async findAll(query: ExecutionQuery = {}): Promise<FlowExecution[]> {
    try {
      let rows: any[];
      const limit = query.limit || 50;
      const offset = query.offset || 0;

      if (query.flowId && query.status) {
        // Filter by both flowId and status - need custom query
        rows = statements.getExecutionsByFlowId.all(query.flowId) as any[];
        rows = rows.filter(row => row.status === query.status);
        rows = rows.slice(offset, offset + limit);
      } else if (query.flowId) {
        rows = statements.getExecutionsByFlowId.all(query.flowId) as any[];
        rows = rows.slice(offset, offset + limit);
      } else if (query.status) {
        rows = statements.getExecutionsByStatus.all(query.status, limit, offset) as any[];
      } else {
        rows = statements.getAllExecutions.all(limit, offset) as any[];
      }

      const executions: FlowExecution[] = [];

      for (const row of rows) {
        // Get logs for each execution
        const logRows = statements.getExecutionLogs.all(row.id) as any[];
        const logs: ExecutionLog[] = logRows.map(logRow => ({
          timestamp: new Date(logRow.timestamp),
          level: logRow.level as 'info' | 'warn' | 'error' | 'debug',
          message: logRow.message,
          stepId: logRow.step_id || undefined,
          data: logRow.data ? JSON.parse(logRow.data) : undefined
        }));

        executions.push({
          id: row.id,
          flowId: row.flow_id,
          customerId: row.customer_id,
          status: row.status as ExecutionStatus,
          startedAt: new Date(row.started_at),
          completedAt: row.completed_at ? new Date(row.completed_at) : undefined,
          results: JSON.parse(row.results || '{}'),
          error: row.error || undefined,
          logs
        });
      }

      console.log(`📋 Found ${executions.length} executions`);
      return executions;
    } catch (error) {
      console.error('❌ Failed to get executions:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = statements.deleteExecution.run(id);
      return result.changes > 0;
    } catch (error) {
      console.error(`❌ Failed to delete execution ${id}:`, error);
      throw error;
    }
  }

  async exists(id: string): Promise<boolean> {
    try {
      const execution = await this.findById(id);
      return !!execution;
    } catch (error) {
      console.error(`❌ Failed to check if execution exists ${id}:`, error);
      throw error;
    }
  }
}

// Singleton storage instance to ensure data persistence across service instances
const globalExecutionStorage = new ExecutionStorage();

export class ExecutionService {
  private storage = globalExecutionStorage;
  private queueService = new QueueService();

  async getExecutions(query: ExecutionQuery = {}): Promise<FlowExecution[]> {
    const executions = await this.storage.findAll(query);

    // Enrich with retry information
    for (const execution of executions) {
      (execution as any).retryInfo = await this.getRetryInfo(execution.id);
    }

    return executions;
  }

  async getExecutionById(id: string): Promise<FlowExecution | null> {
    const execution = await this.storage.findById(id);
    if (execution) {
      (execution as any).retryInfo = await this.getRetryInfo(id);
    }
    return execution;
  }

  private async getRetryInfo(executionId: string): Promise<RetryInfo> {
    try {
      const job = await this.queueService.getJobById(executionId);
      if (job) {
        return {
          attempts: job.attemptsMade,
          maxAttempts: job.opts.attempts || 3,
          isRetry: job.attemptsMade > 1
        };
      }
    } catch (error) {
      // If we can't get job info, return default values
      console.warn(`Could not get retry info for execution ${executionId}:`, error);
    }

    return {
      attempts: 1,
      maxAttempts: 3,
      isRetry: false
    };
  }

  async createExecution(flowId: string, variables: Record<string, any> = {}): Promise<FlowExecution> {
    // Get the flow to extract customerId
    const { flowService } = await import('./flowService');
    const flow = await flowService.getFlowById(flowId);

    if (!flow) {
      throw new Error(`Flow with ID ${flowId} not found`);
    }

    const execution: FlowExecution = {
      id: generateId(),
      flowId,
      customerId: flow.customerId,
      status: 'pending',
      startedAt: new Date(),
      logs: [],
      results: variables
    };

    return this.storage.save(execution);
  }

  async updateExecutionStatus(id: string, status: ExecutionStatus, error?: string): Promise<FlowExecution> {
    try {
      const completedAt = (status === 'completed' || status === 'failed' || status === 'cancelled')
        ? new Date().toISOString()
        : null;

      statements.updateExecutionStatus.run(status, completedAt, error || null, id);

      const execution = await this.storage.findById(id);
      if (!execution) {
        throw new Error('Execution not found');
      }

      console.log(`📊 Updated execution ${id} status to: ${status}`);
      return execution;
    } catch (error) {
      console.error(`❌ Failed to update execution status ${id}:`, error);
      throw error;
    }
  }

  async addExecutionLog(id: string, log: Omit<ExecutionLog, 'timestamp'>): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      const data = log.data ? JSON.stringify(log.data) : null;

      statements.insertExecutionLog.run(
        id,
        timestamp,
        log.level,
        log.message,
        log.stepId || null,
        data
      );

      console.log(`📝 Added log to execution ${id}: [${log.level}] ${log.message}`);
    } catch (error) {
      console.error(`❌ Failed to add log to execution ${id}:`, error);
      throw error;
    }
  }

  async updateExecutionResults(id: string, results: Record<string, any>): Promise<void> {
    try {
      // Get current execution to merge results
      const execution = await this.storage.findById(id);
      if (!execution) {
        throw new Error('Execution not found');
      }

      const mergedResults = { ...execution.results, ...results };
      statements.updateExecutionResults.run(JSON.stringify(mergedResults), id);

      console.log(`📊 Updated execution ${id} results`);
    } catch (error) {
      console.error(`❌ Failed to update execution results ${id}:`, error);
      throw error;
    }
  }

  async getExecutionsByFlowId(flowId: string): Promise<FlowExecution[]> {
    return this.getExecutions({ flowId });
  }

  async getRunningExecutions(): Promise<FlowExecution[]> {
    return this.getExecutions({ status: 'running' });
  }

  async getPendingExecutions(): Promise<FlowExecution[]> {
    return this.getExecutions({ status: 'pending' });
  }

  async deleteExecution(id: string): Promise<void> {
    const deleted = await this.storage.delete(id);
    if (!deleted) {
      throw new Error('Execution not found');
    }
  }

  // Utility methods
  async getExecutionStats(): Promise<{
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  }> {
    const allExecutions = await this.getExecutions({ limit: 10000 }); // Get all
    
    return {
      total: allExecutions.length,
      pending: allExecutions.filter(e => e.status === 'pending').length,
      running: allExecutions.filter(e => e.status === 'running').length,
      completed: allExecutions.filter(e => e.status === 'completed').length,
      failed: allExecutions.filter(e => e.status === 'failed').length,
      cancelled: allExecutions.filter(e => e.status === 'cancelled').length
    };
  }
}
